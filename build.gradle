plugins {
    id 'java'
    id 'io.quarkus'
    id "com.diffplug.spotless" version "7.0.0.BETA2"
    id "com.google.protobuf" version "0.8.19"
    id 'org.kordamp.gradle.jandex' version '2.0.0'
    id 'org.sonarqube' version '5.1.0.4882'
    id "jacoco"
    id "me.champeau.jmh" version "0.7.2"
}
repositories {
    mavenCentral()
    mavenLocal()
    maven {
        url = uri("https://maven.pkg.github.com/HydraXTrader/exchange-generic-aeron-sdk")
        credentials {
            username = project.findProperty("gpr.user") ?: System.getenv("GIT_USERNAME")
            password = project.findProperty("gpr.token") ?: System.getenv("TOKEN")
        }
    }
    maven {
        url = uri("https://maven.pkg.github.com/coralblocks/CoralPool")
        credentials {
            username = project.findProperty("gpr.user") ?: System.getenv("GIT_USERNAME")
            password = project.findProperty("gpr.token") ?: System.getenv("TOKEN")
        }
    }
}

spotless {
    java {
        targetExclude("build/**")
        googleJavaFormat('1.23.0').reflowLongStrings().formatJavadoc(false).reorderImports(false).groupArtifact('com.google.googlejavaformat:google-java-format')
    }
}
quarkus {
    quarkusBuildProperties.put("quarkus.grpc.codegen.proto-directory", "${project.projectDir}/ext/proto")
}
def grpcVersion = '1.44.1'
def protocVersion = '3.19.2'

jandex {
    version = '3.1.7'
}

tasks.named('quarkusDev') {
    dependsOn tasks.named('jandex')
}

quarkusDev {
    jvmArgs = [
            "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
            "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
            "--add-opens=java.base/java.lang=ALL-UNNAMED"
    ]
}

dependencies {
    implementation 'io.quarkiverse.loggingsentry:quarkus-logging-sentry:2.0.7'
    implementation 'io.quarkus:quarkus-smallrye-health'
    implementation 'io.quarkus:quarkus-container-image-docker'
    implementation enforcedPlatform("${quarkusPlatformGroupId}:${quarkusPlatformArtifactId}:${quarkusPlatformVersion}")
    implementation 'org.jboss.slf4j:slf4j-jboss-logmanager'
    implementation 'io.quarkus:quarkus-config-yaml'
    implementation 'io.quarkus:quarkus-arc'
    implementation 'io.smallrye.config:smallrye-config'
    implementation 'io.quarkus:quarkus-jackson:3.15.1'
    implementation 'io.quarkus:quarkus-cache'

    implementation 'io.quarkus:quarkus-kubernetes-client'

    implementation 'io.quarkus:quarkus-reactive-pg-client'
    implementation 'io.quarkus:quarkus-hibernate-reactive-panache'

    testImplementation 'io.quarkus:quarkus-junit5:3.14.3'
    testImplementation 'org.mockito:mockito-core:5.12.0'
    testImplementation 'org.mockito:mockito-junit-jupiter:5.13.0'

    implementation "io.quarkus:quarkus-rest"
    implementation("io.quarkus:quarkus-vertx")

    // Rest Jackson
    implementation("io.quarkus:quarkus-rest-jackson")

    // Lombok
    compileOnly 'org.projectlombok:lombok:1.18.34'
    annotationProcessor 'org.projectlombok:lombok:1.18.34'

    implementation 'org.mapstruct:mapstruct:1.5.5.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'
    testAnnotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'
    // aeron
    implementation 'io.aeron:aeron-all:1.43.0'
    implementation('io.hydrax.aeron:exchange-generic-aeron-sdk:0.3.28-SNAPSHOT') {
        changing = true
    }

    implementation 'com.coralblocks:coralpool:1.4.1'

    // Protobuf
    implementation 'io.grpc:grpc-core:1.66.0'
    implementation 'io.grpc:grpc-stub:1.66.0'
    implementation 'io.grpc:grpc-protobuf:1.66.0'
    implementation 'com.google.protobuf:protobuf-java:3.24.4'
    implementation 'javax.annotation:javax.annotation-api:1.3.2'
    implementation 'com.google.protobuf:protobuf-java-util:3.24.4'

    implementation 'com.google.guava:guava:33.3.0-jre'
    implementation 'org.apache.commons:commons-lang3:3.17.0'
    implementation group: 'net.openhft', name: 'jlbh', version: '1.25ea7'

    implementation 'org.openjdk.jmh:jmh-core:1.37'
    annotationProcessor 'org.openjdk.jmh:jmh-generator-annprocess:1.37'

    // JMH specific dependencies
    jmhImplementation 'org.openjdk.jmh:jmh-core:1.37'
    jmhImplementation 'org.mockito:mockito-core:5.12.0'
    jmhImplementation 'org.mockito:mockito-inline:5.2.0'
    jmhAnnotationProcessor 'org.openjdk.jmh:jmh-generator-annprocess:1.37'

    // Async Profiler for flame graphs - 使用更兼容的版本
    jmhRuntimeOnly 'me.bechberger:ap-loader-all:3.0-9'

    // 备用：如果 async-profiler 不可用，使用 JFR
    jmhRuntimeOnly 'org.openjdk.jmh:jmh-core:1.37'

    implementation 'io.quarkus:quarkus-scheduler'
    implementation group: 'it.unimi.dsi', name: 'fastutil', version: '8.5.15'
    implementation 'org.roaringbitmap:RoaringBitmap:1.3.0'
    implementation 'com.github.chrisvest:stormpot:4.1'
}

group 'io.hydrax.pricestreaming'
version '1.0.0-SNAPSHOT'

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${protocVersion}"
    }
    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:${grpcVersion}"
        }
    }
    generateProtoTasks {
        all()*.plugins {
            grpc {}
        }
    }
}

test {
    systemProperty "java.util.logging.manager", "org.jboss.logmanager.LogManager"
}
compileJava {
    options.encoding = 'UTF-8'
    options.compilerArgs << '-parameters'
}

tasks.build {
    dependsOn 'spotlessApply'
}

compileTestJava {
    options.encoding = 'UTF-8'
}

sourceSets {
    main {
        java {
            srcDirs 'build/generated/source/proto/main/grpc'
            srcDirs 'build/generated/source/proto/main/java'
        }
    }
}

tasks.register('getImageTag') {
    println rootProject.name + ":" + (version as CharSequence)
}

tasks.named('quarkusDependenciesBuild') {
    mustRunAfter(tasks.named('jandex'))
}

configurations.configureEach {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

sonar {
    properties {
        property("sonar.projectKey", "exchange-generic-order-routing-engine")
        property("sonar.host.url", "http://localhost:9000")
        property "sonar.login", "sqa_c992c91eb72d8e910614c18c44d0bd3edf300b1e"
        property "sonar.exclusions", "**/*Generated.java, **/*Entity.java,**/*Dto.java,**/*Controller.java"
    }
}

jacocoTestReport {
    reports {
        xml.required = true
    }
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
}
java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(21))
    }
}

// JMH Configuration
jmh {
    jmhVersion = '1.37'
    includeTests = false
    duplicateClassesStrategy = DuplicatesStrategy.WARN

    // JVM 参数配置，包括火焰图支持
    jvmArgs = [
            '-Xms2G',
            '-Xmx2G',
            '-XX:+UnlockDiagnosticVMOptions',
            '-XX:+DebugNonSafepoints',
            '-XX:+PreserveFramePointer',  // 重要：支持火焰图
            '-XX:+FlightRecorder',        // 启用 JFR
            '-XX:+UseG1GC',              // 使用 G1 GC 以减少 GC 影响
            '-XX:MaxGCPauseMillis=200'   // 限制 GC 暂停时间
    ]

    // JMH specific configurations
    fork = 1
    warmupIterations = 3
    iterations = 5
    timeUnit = 'ns'
    benchmarkMode = ['avgt', 'thrpt']  // 添加吞吐量模式

    // Include all benchmarks (remove filter to run all)
    includes = ['.*OrderEventFullChainBenchmark.*']

    // Profiler 配置 - 支持多种性能分析工具
    // 注意：profilers 配置在运行时通过命令行参数指定
    // profilers = ['async:output=flamegraph']  // 生成火焰图

    // 可选的其他 profiler
    // profilers = ['jfr']  // Java Flight Recorder
    // profilers = ['perf']  // Linux perf (仅限 Linux)
    // profilers = ['perfasm']  // 汇编级别分析

    // Output configuration
    humanOutputFile = project.file("${project.buildDir}/reports/jmh/human.txt")
    resultsFile = project.file("${project.buildDir}/reports/jmh/results.json")
    resultFormat = 'JSON'

    // 火焰图输出目录
    jmhTimeout = '10m'  // 增加超时时间以支持 profiling

    // Fix for "Archive contains more than 65535 entries" error
    zip64 = true

    // Ensure JMH uses the same Java version as the project
    javaLauncher = javaToolchains.launcherFor {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

// Fix JMH dependency issues
tasks.named('compileJmhJava') {
    dependsOn tasks.named('jandex')
}

tasks.named('jmhCompileGeneratedClasses') {
    dependsOn tasks.named('jandex')
}

// 创建专门的火焰图任务
task jmhFlameGraph(type: JavaExec) {
    dependsOn 'jmhJar'
    group = 'verification'
    description = 'Run JMH benchmarks with flame graph generation'

    classpath = sourceSets.jmh.runtimeClasspath
    mainClass = 'org.openjdk.jmh.Main'

    args = [
            '-prof', 'jfr:dir=build/reports/jmh/flamegraphs',  // 使用 JFR 替代 async-profiler
            '-rf', 'json',
            '-rff', 'build/reports/jmh/results-flamegraph.json',
            '-o', 'build/reports/jmh/human-flamegraph.txt',
            '-wi', '3',    // warmup iterations
            '-i', '5',     // measurement iterations
            '-f', '1',     // forks
            '-t', '1',     // threads
            '.*OrderEventFullChainBenchmark.*'  // 使用专门的火焰图测试
    ]

    jvmArgs = [
            '-Xms2G',
            '-Xmx2G',
            '-XX:+UnlockDiagnosticVMOptions',
            '-XX:+DebugNonSafepoints',
            '-XX:+PreserveFramePointer',
            '-XX:+FlightRecorder',
            '-XX:+UseZGC',
            '-XX:MaxGCPauseMillis=200'
    ]

    doFirst {
        // 确保输出目录存在
        file('build/reports/jmh/flamegraphs').mkdirs()
        println "生成火焰图到: build/reports/jmh/flamegraphs/"
    }

    doLast {
        println "JFR 记录生成完成！"
        println "查看 JFR 文件: build/reports/jmh/flamegraphs/"
        println "使用 JDK Mission Control 或以下命令分析:"
        println "jfr summary build/reports/jmh/flamegraphs/*.jfr"
        println ""
        println "要生成火焰图，请使用:"
        println "jfr print --events CPUSample build/reports/jmh/flamegraphs/*.jfr > flamegraph.txt"
    }
}

// 创建 JFR (Java Flight Recorder) 性能分析任务
task jmhJfr(type: JavaExec) {
    dependsOn 'jmhJar'
    group = 'verification'
    description = 'Run JMH benchmarks with Java Flight Recorder'

    classpath = sourceSets.jmh.runtimeClasspath
    mainClass = 'org.openjdk.jmh.Main'

    args = [
            '-prof', 'jfr:dir=build/reports/jmh/jfr',
            '-rf', 'json',
            '-rff', 'build/reports/jmh/results-jfr.json',
            '-o', 'build/reports/jmh/human-jfr.txt',
            '-wi', '2',    // 较少的预热迭代以减少 JFR 文件大小
            '-i', '3',     // 较少的测量迭代
            '-f', '1',
            '.*OrderEventFullChainBenchmark.*'
    ]

    jvmArgs = [
            '-Xms2G',
            '-Xmx2G',
            '-XX:+FlightRecorder',
            '-XX:+UnlockCommercialFeatures',  // 如果使用 Oracle JDK
            '-XX:+UseG1GC'
    ]

    doFirst {
        file('build/reports/jmh/jfr').mkdirs()
        println "生成 JFR 记录到: build/reports/jmh/jfr/"
    }

    doLast {
        println "JFR 记录生成完成！"
        println "JFR 文件: build/reports/jmh/jfr/"
        println "使用 JDK Mission Control 或 jfr 命令行工具分析"
    }
}

// Fix JMH JAR task for large archives
tasks.named('jmhJar') {
    zip64 = true
    duplicatesStrategy = DuplicatesStrategy.WARN
}
