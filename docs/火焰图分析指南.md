# JMH 火焰图分析指南

## 🔥 概述

火焰图是一种可视化性能分析工具，能够直观地显示程序执行过程中的 CPU 使用情况和方法调用栈。本指南将帮助您在 JMH 测试中生成和分析火焰图。

## 🛠️ 配置说明

### 1. Gradle 配置

我们已经在 `build.gradle` 中配置了火焰图支持：

```gradle
jmh {
    // 支持火焰图的 JVM 参数
    jvmArgs = [
        '-Xms2G', '-Xmx2G',
        '-XX:+UnlockDiagnosticVMOptions',
        '-XX:+DebugNonSafepoints',
        '-XX:+PreserveFramePointer',  // 关键：支持火焰图
        '-XX:+FlightRecorder',
        '-XX:+UseG1GC',
        '-XX:MaxGCPauseMillis=200'
    ]
    
    // 配置 async-profiler
    profilers = ['async:output=flamegraph']
}
```

### 2. 依赖配置

```gradle
dependencies {
    // Async Profiler for flame graphs
    jmhRuntimeOnly 'me.bechberger:ap-loader-all:3.0-9'
}
```

## 🚀 使用方法

### 1. 基本用法

```bash
# 生成所有测试的火焰图
./scripts/generate-flamegraph.sh

# 生成特定测试的火焰图
./scripts/generate-flamegraph.sh ".*OrderEventFlameGraphBenchmark.*"

# 生成火焰图并自动打开
./scripts/generate-flamegraph.sh -o

# 生成 JFR 记录
./scripts/generate-flamegraph.sh -j

# 生成火焰图和 JFR 记录
./scripts/generate-flamegraph.sh -a
```

### 2. Gradle 任务

```bash
# 使用 Gradle 任务生成火焰图
./gradlew jmhFlameGraph

# 使用 Gradle 任务生成 JFR 记录
./gradlew jmhJfr

# 标准 JMH 运行（包含火焰图配置）
./gradlew jmh
```

### 3. 脚本选项

```bash
# 显示帮助
./scripts/generate-flamegraph.sh -h

# 清理旧文件
./scripts/generate-flamegraph.sh -c

# 列出现有文件
./scripts/generate-flamegraph.sh -l
```

## 📊 输出文件

### 火焰图文件

生成的文件位于 `build/reports/jmh/flamegraphs/` 目录：

- **`*.html`** - 交互式火焰图（推荐）
- **`*.svg`** - 静态 SVG 火焰图
- **`*.txt`** - 原始性能数据

### JFR 文件

生成的文件位于 `build/reports/jmh/jfr/` 目录：

- **`*.jfr`** - Java Flight Recorder 记录文件

## 🔍 火焰图分析

### 1. 火焰图基础

#### 火焰图结构
- **X 轴**：CPU 使用时间比例
- **Y 轴**：调用栈深度
- **颜色**：不同的方法或类
- **宽度**：方法执行时间占比

#### 关键指标
- **宽度越大**：该方法消耗的 CPU 时间越多
- **高度越高**：调用栈越深
- **平顶**：性能热点（需要优化的地方）

### 2. 性能热点识别

#### 寻找性能瓶颈
1. **查找最宽的方法块**：这些是 CPU 消耗最多的地方
2. **关注平顶区域**：表示该方法直接消耗大量 CPU
3. **分析调用链**：从下往上看调用关系

#### 常见热点模式
- **序列化热点**：`toByteArray()`, `parseFrom()` 等方法
- **GC 热点**：垃圾回收相关的方法
- **锁竞争**：同步相关的方法
- **I/O 热点**：网络或磁盘 I/O 相关方法

### 3. OrderEvent 特定分析

#### 关注的方法
```java
// 主要业务逻辑
io.hydrax.pricestreaming.events.OrderEvent.onOrder

// 序列化相关
com.google.protobuf.*.toByteArray
com.google.protobuf.*.parseFrom

// Mock 相关（测试环境）
org.mockito.*

// JVM 内部
java.lang.Object.*
java.util.*
```

#### 性能优化建议
1. **序列化优化**：
   - 减少不必要的字段
   - 使用更高效的序列化方法
   - 考虑对象池

2. **内存优化**：
   - 减少对象创建
   - 重用对象
   - 优化数据结构

3. **算法优化**：
   - 优化热点方法
   - 减少不必要的计算
   - 使用更高效的算法

## 📈 分析示例

### 1. 正常的火焰图模式

```
OrderEvent.onOrder (80% 宽度)
├── OrderService.placeOrder (40% 宽度)
│   ├── PsOrder.toByteArray (20% 宽度)
│   └── 业务逻辑 (20% 宽度)
└── ClientManager.send (40% 宽度)
    ├── 网络序列化 (25% 宽度)
    └── 网络发送 (15% 宽度)
```

### 2. 有问题的火焰图模式

```
OrderEvent.onOrder (80% 宽度)
├── PsOrder.toByteArray (60% 宽度) ← 序列化热点！
└── 其他逻辑 (20% 宽度)
```

### 3. GC 问题模式

```
OrderEvent.onOrder (80% 宽度)
├── G1YoungGeneration (30% 宽度) ← GC 热点！
├── 业务逻辑 (30% 宽度)
└── 对象分配 (20% 宽度)
```

## 🛠️ 高级分析

### 1. JFR 分析

```bash
# 生成 JFR 记录
./scripts/generate-flamegraph.sh -j

# 使用 jfr 命令行工具分析
jfr summary build/reports/jmh/jfr/*.jfr

# 使用 JDK Mission Control 分析
jmc build/reports/jmh/jfr/*.jfr
```

### 2. 多维度分析

#### CPU 火焰图
- 显示 CPU 使用情况
- 识别计算密集型操作

#### 内存火焰图
```bash
# 生成内存分配火焰图
./gradlew jmhFlameGraph -Pprofiler="async:event=alloc"
```

#### 锁竞争火焰图
```bash
# 生成锁竞争火焰图
./gradlew jmhFlameGraph -Pprofiler="async:event=lock"
```

### 3. 对比分析

```bash
# 优化前
./scripts/generate-flamegraph.sh ".*Before.*"

# 优化后
./scripts/generate-flamegraph.sh ".*After.*"

# 对比两个火焰图，查看优化效果
```

## 🎯 最佳实践

### 1. 测试设计

- **专门的火焰图测试**：使用 `OrderEventFlameGraphBenchmark`
- **足够的运行时间**：确保有足够的样本
- **隔离测试**：避免其他进程干扰

### 2. 环境配置

- **固定的硬件环境**：确保结果可重现
- **关闭不必要的服务**：减少干扰
- **使用生产级别的数据**：确保真实性

### 3. 结果解读

- **关注相对比例**：而不是绝对时间
- **多次运行对比**：确保结果稳定
- **结合业务逻辑**：理解性能热点的业务含义

## 🚨 常见问题

### 1. 火焰图为空或不完整

**原因**：
- JVM 参数配置不正确
- 运行时间太短
- 权限问题

**解决方案**：
```bash
# 确保 JVM 参数正确
-XX:+PreserveFramePointer
-XX:+UnlockDiagnosticVMOptions
-XX:+DebugNonSafepoints

# 增加运行时间
-wi 5 -i 10
```

### 2. 火焰图显示大量 JVM 内部方法

**原因**：测试代码执行时间太短

**解决方案**：
- 增加测试复杂度
- 增加迭代次数
- 使用更真实的数据

### 3. 无法生成火焰图

**原因**：
- async-profiler 不支持当前平台
- 权限不足

**解决方案**：
```bash
# 检查平台支持
java -jar async-profiler.jar list

# 使用 JFR 作为替代
./scripts/generate-flamegraph.sh -j
```

## 📚 参考资源

- [Async Profiler 官方文档](https://github.com/jvm-profiling-tools/async-profiler)
- [火焰图原理](http://www.brendangregg.com/flamegraphs.html)
- [JMH 官方文档](https://openjdk.java.net/projects/code-tools/jmh/)
- [Java Flight Recorder 指南](https://docs.oracle.com/javacomponents/jmc-5-4/jfr-runtime-guide/about.htm)

## ✅ 总结

通过火焰图分析，您可以：

1. **快速识别性能瓶颈**
2. **可视化方法调用关系**
3. **量化性能优化效果**
4. **深入理解程序执行特征**

使用我们提供的工具和配置，您可以轻松地为 OrderEvent 性能测试生成专业级别的火焰图分析报告。
