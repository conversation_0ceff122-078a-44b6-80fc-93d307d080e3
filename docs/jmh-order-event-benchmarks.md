# OrderEvent JMH 性能测试文档

## 概述

本文档描述了为 `OrderEvent.onOrder` 方法创建的 JMH (Java Microbenchmark Harness) 性能测试套件。这些测试旨在测量和监控订单处理的性能特征。

## 测试文件结构

### 1. 主要测试文件

- **`OrderEventBenchmark.java`** - 全面的性能测试套件
  - 包含多种订单类型测试（新订单、取消订单、编辑订单等）
  - 测试不同的订单复杂度（简单、复杂、大数量等）
  - 包含吞吐量、并发性和错误处理测试

- **`OrderEventSimpleBenchmark.java`** - 简化的性能测试
  - 专注于核心功能测试
  - 使用 Mockito 模拟依赖项
  - 更快的执行时间，适合快速验证

- **`OrderEventFullChainBenchmark.java`** - 完整链路性能测试 ⭐
  - **Mock aeronCluster.offer 和 publication.offer**
  - 测试从 OrderEvent.onOrder 到 ClientManager 内部方法的完整调用链路
  - 包含 Aeron 组件的详细性能分析
  - 支持并发和吞吐量测试

- **`OrderEventPerformanceBenchmark.java`** - 专门的性能优化测试
  - 测试缓存命中/未命中场景
  - 内存分配模式测试
  - 错误恢复性能测试

## 测试结果分析

### 当前性能基准（基于 OrderEventFullChainBenchmark - 完整链路测试）

| 测试场景 | 平均时间 (ns/op) | 标准差 | 说明 |
|---------|-----------------|--------|------|
| **完整链路测试** | | | |
| 新订单完整链路 | 64,642 | ±78,330 | 包含 mock aeronCluster.offer 的完整处理链路 |
| 市场订单完整链路 | 67,401 | ±45,799 | 市场订单的完整处理链路 |
| 取消订单完整链路 | 60,468 | ±80,739 | 取消订单的完整处理链路 |
| 完整链路并发 (4线程) | 188,314 | ±151,755 | 4线程并发完整链路性能 |
| 完整链路吞吐量 | 74,245 | ±313,532 | 批量处理完整链路性能 |
| **Aeron 组件测试** | | | |
| AeronCluster.offer | 15,771 | ±20,430 | 模拟 aeronCluster.offer 调用 |
| Publication.offer | 11,944 | ±47,306 | 模拟 publication.offer 调用 |
| ClientManager.send | 32,878 | ±348,007 | 模拟 ClientManager.send 调用 |
| ClientManager.sendToOwn | 12,192 | ±43,048 | 模拟 ClientManager.sendToOwn 调用 |
| **并发测试** | | | |
| AeronCluster 并发 (8线程) | 54,960 | ±48,626 | 8线程并发 aeronCluster.offer |

### 性能观察

1. **完整链路性能** - 包含 Aeron 组件 mock 的完整链路平均 60-70μs
2. **Aeron 组件开销** - publication.offer (~12μs) 比 aeronCluster.offer (~16μs) 稍快
3. **ClientManager 层开销** - ClientManager.send 有较大变异性，可能需要优化
4. **并发性能** - 8线程并发 aeronCluster.offer 性能良好 (~55μs)
5. **取消订单效率** - 取消订单在完整链路中表现最好 (~60μs)
6. **Mock 真实性** - 通过模拟网络延迟和序列化开销，更接近真实环境

## 依赖配置修复

### 问题
原始配置中 Mockito 依赖只在 `testImplementation` 中，JMH 测试无法访问。

### 解决方案
在 `build.gradle` 中添加了 JMH 特定的依赖：

```gradle
// JMH specific dependencies
jmhImplementation 'org.openjdk.jmh:jmh-core:1.37'
jmhImplementation 'org.mockito:mockito-core:5.12.0'
jmhImplementation 'org.mockito:mockito-inline:5.2.0'
jmhAnnotationProcessor 'org.openjdk.jmh:jmh-generator-annprocess:1.37'
```

## 运行测试

### 基本命令

```bash
# 运行完整链路测试（推荐）
./gradlew jmh  # 当前配置运行 OrderEventFullChainBenchmark

# 运行简化测试套件
# 修改 build.gradle 中的 includes = ['.*OrderEventSimpleBenchmark.*']
./gradlew jmh

# 运行特定测试
./gradlew jmh --include=".*onOrderNewOrderFullChain.*"

# 运行 Aeron 组件测试
./gradlew jmh --include=".*aeronClusterOfferOnly.*"
```

### 测试配置

当前 JMH 配置：
- **预热迭代**: 2次，每次1秒
- **测量迭代**: 3次，每次2秒
- **JVM 参数**: `-Xms1G -Xmx1G`
- **超时**: 每次迭代10分钟

## 测试类型

### 1. 基本功能测试
- `onOrderNewOrder` - 新订单处理
- `onOrderCancelOrder` - 取消订单处理
- `onOrderMarketOrder` - 市场订单处理

### 2. 吞吐量测试
- `onOrderThroughputMixed` - 混合订单类型吞吐量
- `onOrderThroughputNewOrdersOnly` - 仅新订单吞吐量

### 3. 并发测试
- `onOrderConcurrent` - 多线程并发处理

### 4. 复杂度测试（在完整测试套件中）
- 不同订单大小和复杂度
- 不同市场和交易对
- 边界情况和错误处理

## 性能目标

基于测试结果，建议的性能目标：

| 指标 | 目标值 | 可接受值 | 警告阈值 |
|------|--------|----------|----------|
| 新订单处理 | < 10μs | < 20μs | > 50μs |
| 市场订单处理 | < 5μs | < 15μs | > 30μs |
| 取消订单处理 | < 15μs | < 30μs | > 100μs |
| 并发处理 (4线程) | < 25μs | < 50μs | > 100μs |

## 监控和持续改进

### 建议的监控策略

1. **定期运行基准测试** - 在每次重要代码更改后
2. **性能回归检测** - 如果性能下降超过20%则发出警告
3. **趋势分析** - 跟踪长期性能趋势
4. **环境一致性** - 确保测试环境的一致性

### 优化建议

1. **取消订单优化** - 减少取消订单处理的变异性
2. **缓存优化** - 实现更好的缓存策略
3. **内存管理** - 减少对象分配和GC压力
4. **并发优化** - 进一步优化高并发场景

## 故障排除

### 常见问题

1. **Mockito 警告** - 这是正常的，不影响测试结果
2. **JVM 警告** - 关于动态代理加载的警告是预期的
3. **高变异性** - 可能需要增加预热迭代次数

### 调试技巧

1. 使用 `-prof` 参数进行性能分析
2. 增加预热迭代次数以获得更稳定的结果
3. 使用不同的 JVM 参数进行测试

## 结论

OrderEvent JMH 测试套件已成功实现并运行。测试显示了良好的基准性能，为未来的性能优化和监控提供了基础。建议定期运行这些测试以确保性能不会回归，并在进行重大代码更改时使用这些基准进行验证。
