#!/bin/bash

# JMH 火焰图生成脚本
# 使用方法: ./scripts/generate-flamegraph.sh [benchmark_pattern]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认基准测试模式
BENCHMARK_PATTERN=${1:-".*OrderEventFullChainBenchmark.*"}

echo -e "${BLUE}🔥 JMH 火焰图生成工具${NC}"
echo -e "${BLUE}================================${NC}"

# 检查操作系统
OS=$(uname -s)
echo -e "${YELLOW}检测到操作系统: $OS${NC}"

# 创建输出目录
OUTPUT_DIR="build/reports/jmh/flamegraphs"
JFR_DIR="build/reports/jmh/jfr"
mkdir -p "$OUTPUT_DIR"
mkdir -p "$JFR_DIR"

echo -e "${YELLOW}输出目录: $OUTPUT_DIR${NC}"

# 检查 Java 版本
JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
echo -e "${YELLOW}Java 版本: $JAVA_VERSION${NC}"

# 函数：生成火焰图
generate_flamegraph() {
    echo -e "${GREEN}🚀 开始生成火焰图...${NC}"
    echo -e "${YELLOW}基准测试模式: $BENCHMARK_PATTERN${NC}"
    
    # 运行 JMH 火焰图任务
    ./gradlew jmhFlameGraph \
        -Pjmh.includes="$BENCHMARK_PATTERN" \
        --info
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 火焰图生成成功！${NC}"
        list_flamegraph_files
    else
        echo -e "${RED}❌ 火焰图生成失败${NC}"
        exit 1
    fi
}

# 函数：生成 JFR 记录
generate_jfr() {
    echo -e "${GREEN}📊 开始生成 JFR 记录...${NC}"
    
    ./gradlew jmhJfr \
        -Pjmh.includes="$BENCHMARK_PATTERN" \
        --info
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ JFR 记录生成成功！${NC}"
        list_jfr_files
    else
        echo -e "${RED}❌ JFR 记录生成失败${NC}"
        exit 1
    fi
}

# 函数：列出火焰图文件
list_flamegraph_files() {
    echo -e "${BLUE}📁 生成的火焰图文件:${NC}"
    if [ -d "$OUTPUT_DIR" ]; then
        find "$OUTPUT_DIR" -name "*.html" -o -name "*.svg" -o -name "*.txt" | while read file; do
            echo -e "${GREEN}  📄 $file${NC}"
        done
        
        # 查找 HTML 文件并提供打开建议
        HTML_FILES=$(find "$OUTPUT_DIR" -name "*.html" | head -5)
        if [ ! -z "$HTML_FILES" ]; then
            echo -e "${YELLOW}💡 打开火焰图 HTML 文件:${NC}"
            echo "$HTML_FILES" | while read html_file; do
                echo -e "${BLUE}  🌐 open $html_file${NC}"
            done
        fi
    else
        echo -e "${RED}❌ 火焰图目录不存在${NC}"
    fi
}

# 函数：列出 JFR 文件
list_jfr_files() {
    echo -e "${BLUE}📁 生成的 JFR 文件:${NC}"
    if [ -d "$JFR_DIR" ]; then
        find "$JFR_DIR" -name "*.jfr" | while read file; do
            echo -e "${GREEN}  📄 $file${NC}"
            echo -e "${BLUE}    分析命令: jfr summary $file${NC}"
        done
    else
        echo -e "${RED}❌ JFR 目录不存在${NC}"
    fi
}

# 函数：打开火焰图
open_flamegraph() {
    HTML_FILE=$(find "$OUTPUT_DIR" -name "*.html" | head -1)
    if [ ! -z "$HTML_FILE" ]; then
        echo -e "${GREEN}🌐 打开火焰图: $HTML_FILE${NC}"
        case "$OS" in
            "Darwin")  # macOS
                open "$HTML_FILE"
                ;;
            "Linux")
                xdg-open "$HTML_FILE" 2>/dev/null || echo -e "${YELLOW}请手动打开: $HTML_FILE${NC}"
                ;;
            *)
                echo -e "${YELLOW}请手动打开: $HTML_FILE${NC}"
                ;;
        esac
    else
        echo -e "${RED}❌ 未找到 HTML 火焰图文件${NC}"
    fi
}

# 函数：清理旧文件
clean_old_files() {
    echo -e "${YELLOW}🧹 清理旧的性能分析文件...${NC}"
    rm -rf "$OUTPUT_DIR"/*
    rm -rf "$JFR_DIR"/*
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 函数：显示帮助
show_help() {
    echo -e "${BLUE}使用方法:${NC}"
    echo -e "  $0 [选项] [基准测试模式]"
    echo -e ""
    echo -e "${BLUE}选项:${NC}"
    echo -e "  -f, --flamegraph    生成火焰图 (默认)"
    echo -e "  -j, --jfr          生成 JFR 记录"
    echo -e "  -a, --all          生成火焰图和 JFR 记录"
    echo -e "  -o, --open         生成后自动打开火焰图"
    echo -e "  -c, --clean        清理旧文件"
    echo -e "  -l, --list         列出现有文件"
    echo -e "  -h, --help         显示帮助"
    echo -e ""
    echo -e "${BLUE}示例:${NC}"
    echo -e "  $0                                    # 生成所有基准测试的火焰图"
    echo -e "  $0 '.*onOrderNewOrderFullChain.*'     # 生成特定测试的火焰图"
    echo -e "  $0 -j                                 # 生成 JFR 记录"
    echo -e "  $0 -a -o                              # 生成所有类型并自动打开"
}

# 主逻辑
GENERATE_FLAMEGRAPH=true
GENERATE_JFR=false
AUTO_OPEN=false
CLEAN_FILES=false
LIST_FILES=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--flamegraph)
            GENERATE_FLAMEGRAPH=true
            GENERATE_JFR=false
            shift
            ;;
        -j|--jfr)
            GENERATE_FLAMEGRAPH=false
            GENERATE_JFR=true
            shift
            ;;
        -a|--all)
            GENERATE_FLAMEGRAPH=true
            GENERATE_JFR=true
            shift
            ;;
        -o|--open)
            AUTO_OPEN=true
            shift
            ;;
        -c|--clean)
            CLEAN_FILES=true
            shift
            ;;
        -l|--list)
            LIST_FILES=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        -*)
            echo -e "${RED}❌ 未知选项: $1${NC}"
            show_help
            exit 1
            ;;
        *)
            BENCHMARK_PATTERN="$1"
            shift
            ;;
    esac
done

# 执行操作
if [ "$CLEAN_FILES" = true ]; then
    clean_old_files
fi

if [ "$LIST_FILES" = true ]; then
    list_flamegraph_files
    list_jfr_files
    exit 0
fi

# 编译项目
echo -e "${YELLOW}🔨 编译 JMH 测试...${NC}"
./gradlew compileJmhJava
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 编译失败${NC}"
    exit 1
fi

# 生成性能分析文件
if [ "$GENERATE_FLAMEGRAPH" = true ]; then
    generate_flamegraph
fi

if [ "$GENERATE_JFR" = true ]; then
    generate_jfr
fi

# 自动打开火焰图
if [ "$AUTO_OPEN" = true ] && [ "$GENERATE_FLAMEGRAPH" = true ]; then
    open_flamegraph
fi

echo -e "${GREEN}🎉 性能分析完成！${NC}"
