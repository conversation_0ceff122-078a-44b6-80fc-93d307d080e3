#!/bin/bash

# JFR 火焰图生成脚本（简化版）
# 使用方法: ./scripts/generate-jfr-flamegraph.sh [benchmark_pattern]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认基准测试模式
BENCHMARK_PATTERN=${1:-".*OrderEventFlameGraphBenchmark.*"}

echo -e "${BLUE}🔥 JFR 性能分析工具${NC}"
echo -e "${BLUE}================================${NC}"

# 检查操作系统
OS=$(uname -s)
echo -e "${YELLOW}检测到操作系统: $OS${NC}"

# 创建输出目录
OUTPUT_DIR="build/reports/jmh/jfr"
mkdir -p "$OUTPUT_DIR"

echo -e "${YELLOW}输出目录: $OUTPUT_DIR${NC}"

# 检查 Java 版本
JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
echo -e "${YELLOW}Java 版本: $JAVA_VERSION${NC}"

# 函数：生成 JFR 记录
generate_jfr() {
    echo -e "${GREEN}📊 开始生成 JFR 记录...${NC}"
    echo -e "${YELLOW}基准测试模式: $BENCHMARK_PATTERN${NC}"
    
    # 编译项目
    echo -e "${YELLOW}🔨 编译 JMH 测试...${NC}"
    ./gradlew compileJmhJava
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 编译失败${NC}"
        exit 1
    fi
    
    # 运行 JMH 与 JFR
    echo -e "${GREEN}🚀 运行 JMH 基准测试（带 JFR 记录）...${NC}"
    ./gradlew jmhFlameGraph \
        -Pjmh.includes="$BENCHMARK_PATTERN" \
        --info
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ JFR 记录生成成功！${NC}"
        list_jfr_files
        analyze_jfr_files
    else
        echo -e "${RED}❌ JFR 记录生成失败${NC}"
        exit 1
    fi
}

# 函数：列出 JFR 文件
list_jfr_files() {
    echo -e "${BLUE}📁 生成的 JFR 文件:${NC}"
    if [ -d "$OUTPUT_DIR" ]; then
        find "$OUTPUT_DIR" -name "*.jfr" | while read file; do
            echo -e "${GREEN}  📄 $file${NC}"
            file_size=$(ls -lh "$file" | awk '{print $5}')
            echo -e "${BLUE}    大小: $file_size${NC}"
        done
    else
        echo -e "${RED}❌ JFR 目录不存在${NC}"
    fi
}

# 函数：分析 JFR 文件
analyze_jfr_files() {
    echo -e "${BLUE}🔍 分析 JFR 文件...${NC}"
    
    JFR_FILE=$(find "$OUTPUT_DIR" -name "*.jfr" | head -1)
    if [ ! -z "$JFR_FILE" ]; then
        echo -e "${GREEN}分析文件: $JFR_FILE${NC}"
        
        # 检查是否有 jfr 命令
        if command -v jfr &> /dev/null; then
            echo -e "${YELLOW}📊 JFR 摘要信息:${NC}"
            jfr summary "$JFR_FILE"
            
            echo -e "${YELLOW}🔥 生成 CPU 采样数据...${NC}"
            CPU_OUTPUT="$OUTPUT_DIR/cpu-samples.txt"
            jfr print --events CPUSample "$JFR_FILE" > "$CPU_OUTPUT"
            echo -e "${GREEN}CPU 采样数据保存到: $CPU_OUTPUT${NC}"
            
            echo -e "${YELLOW}🧠 生成内存分配数据...${NC}"
            ALLOC_OUTPUT="$OUTPUT_DIR/allocation-samples.txt"
            jfr print --events ObjectAllocationInNewTLAB,ObjectAllocationOutsideTLAB "$JFR_FILE" > "$ALLOC_OUTPUT" 2>/dev/null || echo "内存分配事件不可用"
            
            echo -e "${YELLOW}🔒 生成锁竞争数据...${NC}"
            LOCK_OUTPUT="$OUTPUT_DIR/lock-samples.txt"
            jfr print --events JavaMonitorEnter,JavaMonitorWait "$JFR_FILE" > "$LOCK_OUTPUT" 2>/dev/null || echo "锁竞争事件不可用"
            
        else
            echo -e "${YELLOW}⚠️  jfr 命令不可用，请使用 JDK Mission Control 分析${NC}"
        fi
        
        # 提供分析建议
        provide_analysis_suggestions "$JFR_FILE"
    else
        echo -e "${RED}❌ 未找到 JFR 文件${NC}"
    fi
}

# 函数：提供分析建议
provide_analysis_suggestions() {
    local jfr_file="$1"
    
    echo -e "${BLUE}💡 分析建议:${NC}"
    echo -e "${YELLOW}1. 使用 JDK Mission Control:${NC}"
    echo -e "   jmc $jfr_file"
    echo -e ""
    echo -e "${YELLOW}2. 命令行分析:${NC}"
    echo -e "   # 查看热点方法"
    echo -e "   jfr print --events CPUSample $jfr_file | grep -A 5 -B 5 'OrderEvent'"
    echo -e ""
    echo -e "   # 查看 GC 信息"
    echo -e "   jfr print --events GarbageCollection $jfr_file"
    echo -e ""
    echo -e "   # 查看方法执行时间"
    echo -e "   jfr print --events ExecutionSample $jfr_file"
    echo -e ""
    echo -e "${YELLOW}3. 在线火焰图工具:${NC}"
    echo -e "   访问 https://jfr.jvm.tools/ 上传 JFR 文件生成火焰图"
    echo -e ""
    echo -e "${YELLOW}4. 本地火焰图生成:${NC}"
    echo -e "   如果安装了 FlameGraph 工具:"
    echo -e "   jfr print --events CPUSample $jfr_file | ./FlameGraph/stackcollapse-jfr.pl | ./FlameGraph/flamegraph.pl > flamegraph.svg"
}

# 函数：清理旧文件
clean_old_files() {
    echo -e "${YELLOW}🧹 清理旧的 JFR 文件...${NC}"
    rm -rf "$OUTPUT_DIR"/*
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 函数：显示帮助
show_help() {
    echo -e "${BLUE}使用方法:${NC}"
    echo -e "  $0 [选项] [基准测试模式]"
    echo -e ""
    echo -e "${BLUE}选项:${NC}"
    echo -e "  -c, --clean        清理旧文件"
    echo -e "  -l, --list         列出现有文件"
    echo -e "  -a, --analyze      分析现有 JFR 文件"
    echo -e "  -h, --help         显示帮助"
    echo -e ""
    echo -e "${BLUE}示例:${NC}"
    echo -e "  $0                                    # 生成所有基准测试的 JFR 记录"
    echo -e "  $0 '.*hotspotNewOrderProcessing.*'    # 生成特定测试的 JFR 记录"
    echo -e "  $0 -a                                 # 分析现有 JFR 文件"
    echo -e "  $0 -c                                 # 清理旧文件"
}

# 主逻辑
CLEAN_FILES=false
LIST_FILES=false
ANALYZE_ONLY=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--clean)
            CLEAN_FILES=true
            shift
            ;;
        -l|--list)
            LIST_FILES=true
            shift
            ;;
        -a|--analyze)
            ANALYZE_ONLY=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        -*)
            echo -e "${RED}❌ 未知选项: $1${NC}"
            show_help
            exit 1
            ;;
        *)
            BENCHMARK_PATTERN="$1"
            shift
            ;;
    esac
done

# 执行操作
if [ "$CLEAN_FILES" = true ]; then
    clean_old_files
fi

if [ "$LIST_FILES" = true ]; then
    list_jfr_files
    exit 0
fi

if [ "$ANALYZE_ONLY" = true ]; then
    analyze_jfr_files
    exit 0
fi

# 生成 JFR 记录
generate_jfr

echo -e "${GREEN}🎉 JFR 性能分析完成！${NC}"
echo -e "${BLUE}💡 提示: 使用 -a 选项可以重新分析现有的 JFR 文件${NC}"
