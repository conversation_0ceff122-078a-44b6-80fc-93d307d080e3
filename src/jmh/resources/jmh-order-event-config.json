{"name": "OrderEvent Performance Benchmark Configuration", "description": "Comprehensive JMH configuration for OrderEvent.onOrder method performance testing", "version": "1.0.0", "benchmarkConfigurations": {"quick": {"description": "Quick performance check", "includes": [".*OrderEventBenchmark.onOrderNewOrder.*", ".*OrderEventBenchmark.onOrderCancelOrder.*"], "warmupIterations": 2, "measurementIterations": 3, "forks": 1, "timeUnit": "ns", "mode": ["avgt"]}, "comprehensive": {"description": "Comprehensive performance analysis", "includes": [".*OrderEventBenchmark.*"], "excludes": [".*HighConcurrency.*", ".*SteadyState.*"], "warmupIterations": 3, "measurementIterations": 5, "forks": 1, "timeUnit": "ns", "mode": ["avgt", "thrpt"]}, "throughput": {"description": "Throughput-focused testing", "includes": [".*OrderEventBenchmark.*Throughput.*"], "warmupIterations": 5, "measurementIterations": 10, "forks": 2, "timeUnit": "ops/s", "mode": ["thrpt"]}, "latency": {"description": "Latency-focused testing", "includes": [".*OrderEventBenchmark.onOrder.*Order$", ".*OrderEventBenchmark.*LatencySensitive.*"], "warmupIterations": 5, "measurementIterations": 10, "forks": 2, "timeUnit": "ns", "mode": ["avgt"]}, "concurrency": {"description": "Concurrency performance testing", "includes": [".*OrderEventBenchmark.*Concurrency.*", ".*OrderEventBenchmark.*Concurrent.*"], "warmupIterations": 3, "measurementIterations": 5, "forks": 1, "timeUnit": "ops/s", "mode": ["thrpt"]}, "stress": {"description": "Stress testing with high load", "includes": [".*OrderEventBenchmark.*Burst.*", ".*OrderEventBenchmark.*SteadyState.*", ".*OrderEventBenchmark.*Variable.*"], "warmupIterations": 5, "measurementIterations": 10, "forks": 1, "timeUnit": "ops/s", "mode": ["thrpt"]}, "memory": {"description": "Memory allocation and GC impact testing", "includes": [".*OrderEventBenchmark.*GC.*", ".*OrderEventBenchmark.*Memory.*"], "warmupIterations": 3, "measurementIterations": 5, "forks": 1, "timeUnit": "ns", "mode": ["avgt"]}, "errorHandling": {"description": "Error handling performance testing", "includes": [".*OrderEventBenchmark.*Error.*", ".*OrderEventBenchmark.*Invalid.*", ".*OrderEventBenchmark.*Exception.*"], "warmupIterations": 2, "measurementIterations": 3, "forks": 1, "timeUnit": "ns", "mode": ["avgt"]}}, "jvmOptions": {"default": ["-Xms2G", "-Xmx2G", "-XX:+UseG1GC"], "lowMemory": ["-Xms512M", "-Xmx1G", "-XX:+UseG1GC"], "highMemory": ["-Xms4G", "-Xmx8G", "-XX:+UseG1GC"], "gcAnalysis": ["-Xms2G", "-Xmx2G", "-XX:+UseG1GC", "-XX:+PrintGC", "-XX:+PrintGCDetails"], "profiling": ["-Xms2G", "-Xmx2G", "-XX:+UseG1GC", "-XX:+UnlockDiagnosticVMOptions", "-XX:+TraceClassLoading"]}, "expectedResults": {"onOrderNewOrder": {"averageTime": {"target": "< 1000 ns", "acceptable": "< 5000 ns", "warning": "> 10000 ns"}, "throughput": {"target": "> 1000000 ops/s", "acceptable": "> 500000 ops/s", "warning": "< 100000 ops/s"}}, "onOrderCancelOrder": {"averageTime": {"target": "< 2000 ns", "acceptable": "< 8000 ns", "warning": "> 15000 ns"}, "throughput": {"target": "> 500000 ops/s", "acceptable": "> 250000 ops/s", "warning": "< 50000 ops/s"}}, "onOrderMarketOrder": {"averageTime": {"target": "< 500 ns", "acceptable": "< 2000 ns", "warning": "> 5000 ns"}, "throughput": {"target": "> 2000000 ops/s", "acceptable": "> 1000000 ops/s", "warning": "< 200000 ops/s"}}, "onOrderComplexOrder": {"averageTime": {"target": "< 3000 ns", "acceptable": "< 10000 ns", "warning": "> 20000 ns"}, "throughput": {"target": "> 300000 ops/s", "acceptable": "> 100000 ops/s", "warning": "< 50000 ops/s"}}}, "runCommands": {"quick": "./gradlew jmh -Pjmh.includes='.*OrderEventBenchmark.onOrderNewOrder.*,.*OrderEventBenchmark.onOrderCancelOrder.*' -Pjmh.warmupIterations=2 -Pjmh.iterations=3", "comprehensive": "./gradlew jmh -Pjmh.includes='.*OrderEventBenchmark.*' -Pjmh.excludes='.*HighConcurrency.*,.*SteadyState.*' -Pjmh.warmupIterations=3 -Pjmh.iterations=5", "throughput": "./gradlew jmh -Pjmh.includes='.*OrderEventBenchmark.*Throughput.*' -Pjmh.benchmarkMode=thrpt -Pjmh.warmupIterations=5 -Pjmh.iterations=10", "latency": "./gradlew jmh -Pjmh.includes='.*OrderEventBenchmark.onOrder.*Order$,.*OrderEventBenchmark.*LatencySensitive.*' -Pjmh.benchmarkMode=avgt -Pjmh.timeUnit=ns", "concurrency": "./gradlew jmh -Pjmh.includes='.*OrderEventBenchmark.*Concurrency.*' -Pjmh.benchmarkMode=thrpt", "stress": "./gradlew jmh -Pjmh.includes='.*OrderEventBenchmark.*Burst.*,.*OrderEventBenchmark.*SteadyState.*' -Pjmh.jvmArgs='-Xms4G,-Xmx8G,-XX:+UseG1GC'", "memory": "./gradlew jmh -Pjmh.includes='.*OrderEventBenchmark.*GC.*,.*OrderEventBenchmark.*Memory.*' -Pjmh.jvmArgs='-Xms2G,-Xmx2G,-XX:+UseG1GC,-XX:+PrintGC'", "errorHandling": "./gradlew jmh -Pjmh.includes='.*OrderEventBenchmark.*Error.*,.*OrderEventBenchmark.*Invalid.*'"}, "reportingOptions": {"outputFormats": ["JSON", "CSV", "TEXT"], "resultFile": "build/reports/jmh/order-event-results.json", "humanOutputFile": "build/reports/jmh/order-event-human.txt", "csvFile": "build/reports/jmh/order-event-results.csv"}, "analysisGuidelines": {"performanceRegression": {"threshold": "20%", "description": "Alert if performance degrades by more than 20% compared to baseline"}, "memoryLeaks": {"description": "Monitor for increasing memory usage across iterations"}, "concurrencyIssues": {"description": "Check for performance degradation under high concurrency"}, "errorHandlingOverhead": {"description": "Ensure error handling doesn't significantly impact normal operation performance"}}}