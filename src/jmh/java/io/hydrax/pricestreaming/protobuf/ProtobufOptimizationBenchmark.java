package io.hydrax.pricestreaming.protobuf;

import com.google.protobuf.InvalidProtocolBufferException;
import io.hydrax.pricestreaming.utils.ProtobufOptimizationUtil;
import io.hydrax.pricestreaming.utils.UDec128Util;
import io.hydrax.proto.metwo.match.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;

/**
 * JMH Benchmark comparing standard protobuf operations vs optimized versions.
 *
 * This benchmark demonstrates the performance improvements from:
 * - Builder reuse
 * - Pre-allocated streams
 * - Cached reflection
 * - Batch operations
 *
 * Run with:
 * ./gradlew jmh --include=".*ProtobufOptimizationBenchmark.*"
 */
@BenchmarkMode({Mode.AverageTime, Mode.Throughput})
@OutputTimeUnit(TimeUnit.NANOSECONDS)
@State(Scope.Benchmark)
@Fork(
    value = 1,
    jvmArgs = {"-Xms2G", "-Xmx2G", "-XX:+UseG1GC"})
@Warmup(iterations = 3, time = 2, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 5, time = 3, timeUnit = TimeUnit.SECONDS)
public class ProtobufOptimizationBenchmark {

  private List<PsOrder> orderList;
  private List<Response> responseList;
  private byte[] serializedOrderBytes;
  private byte[] batchSerializedBytes;

  @Setup
  public void setup() throws IOException {
    setupTestData();
    setupSerializedData();
  }

  private void setupTestData() {
    orderList = new ArrayList<>();
    responseList = new ArrayList<>();

    for (int i = 0; i < 100; i++) {
      // Create orders for testing
      PsOrder order =
          PsOrder.newBuilder()
              .setOrderId("ORDER_" + String.format("%06d", i))
              .setSymbol("BTC/USD")
              .setSide(i % 2 == 0 ? Side.SIDE_BUY : Side.SIDE_SELL)
              .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
              .setQty(UDec128Util.from("1.0"))
              .setPrice(UDec128Util.from("50000.0"))
              .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
              .build();
      orderList.add(order);

      // Create responses for testing
      Response response =
          Response.newBuilder()
              .setExecReport(
                  ExecReport.newBuilder()
                      .setOrderId("ORDER_" + String.format("%06d", i))
                      .setExecType(ExecType.EXEC_TYPE_NEW)
                      .setOrdStatus(OrdStatus.ORD_STATUS_NEW)
                      .build())
              .build();
      responseList.add(response);
    }
  }

  private void setupSerializedData() throws IOException {
    // Serialize a single order for deserialization tests
    serializedOrderBytes = orderList.get(0).toByteArray();

    // Create batch serialized data
    batchSerializedBytes = ProtobufOptimizationUtil.serializeBatch(orderList);
  }

  // ========== Builder Reuse Benchmarks ==========

  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  public void createOrderStandard(Blackhole bh) {
    PsOrder order =
        PsOrder.newBuilder()
            .setOrderId("STANDARD_ORDER")
            .setSymbol("BTC/USD")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(UDec128Util.from("1.0"))
            .setPrice(UDec128Util.from("50000.0"))
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
            .build();
    bh.consume(order);
  }

  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  public void createOrderOptimized(Blackhole bh) {
    PsOrder order =
        ProtobufOptimizationUtil.createOptimizedOrder(
            "OPTIMIZED_ORDER",
            "BTC/USD",
            Side.SIDE_BUY,
            PsOrderType.PS_ORDER_TYPE_LIMIT,
            "1.0",
            "50000.0",
            TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL);
    bh.consume(order);
  }

  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  public void createOrderWithReusedBuilder(Blackhole bh) {
    PsOrder order =
        ProtobufOptimizationUtil.getReusableOrderBuilder()
            .setOrderId("REUSED_BUILDER_ORDER")
            .setSymbol("BTC/USD")
            .setSide(Side.SIDE_BUY)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setQty(UDec128Util.from("1.0"))
            .setPrice(UDec128Util.from("50000.0"))
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
            .build();
    bh.consume(order);
  }

  // ========== Serialization Optimization Benchmarks ==========

  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  public void serializeStandard(Blackhole bh) {
    byte[] result = orderList.get(0).toByteArray();
    bh.consume(result);
  }

  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  public void serializeOptimized(Blackhole bh) throws IOException {
    byte[] result = ProtobufOptimizationUtil.serializeOptimized(orderList.get(0));
    bh.consume(result);
  }

  // ========== Deserialization Optimization Benchmarks ==========

  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  public void deserializeStandard(Blackhole bh) throws InvalidProtocolBufferException {
    PsOrder result = PsOrder.parseFrom(serializedOrderBytes);
    bh.consume(result);
  }

  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  public void deserializeOptimized(Blackhole bh) throws InvalidProtocolBufferException {
    PsOrder result =
        ProtobufOptimizationUtil.parseFromOptimized(serializedOrderBytes, PsOrder.class);
    bh.consume(result);
  }

  // ========== Batch Operation Benchmarks ==========

  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  public void serializeBatchStandard(Blackhole bh) {
    List<byte[]> results = new ArrayList<>();
    for (PsOrder order : orderList) {
      results.add(order.toByteArray());
    }
    bh.consume(results);
  }

  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  public void serializeBatchOptimized(Blackhole bh) throws IOException {
    byte[] result = ProtobufOptimizationUtil.serializeBatch(orderList);
    bh.consume(result);
  }

  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  public void deserializeBatchOptimized(Blackhole bh) throws InvalidProtocolBufferException {
    List<PsOrder> result = ProtobufOptimizationUtil.parseBatch(batchSerializedBytes, PsOrder.class);
    bh.consume(result);
  }

  // ========== ResponseList Creation Benchmarks ==========

  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  public void createResponseListStandard(Blackhole bh) {
    ResponseList.Builder builder =
        ResponseList.newBuilder().setOutSequence(System.currentTimeMillis());

    for (Response response : responseList) {
      builder.addResponses(response);
    }

    ResponseList result = builder.build();
    bh.consume(result);
  }

  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  public void createResponseListOptimized(Blackhole bh) {
    ResponseList result =
        ProtobufOptimizationUtil.createOptimizedResponseList(
            System.currentTimeMillis(), responseList);
    bh.consume(result);
  }

  // ========== Throughput Benchmarks ==========

  @Benchmark
  @BenchmarkMode(Mode.Throughput)
  @OperationsPerInvocation(100)
  public void throughputCreateOrdersStandard(Blackhole bh) {
    for (int i = 0; i < 100; i++) {
      PsOrder order =
          PsOrder.newBuilder()
              .setOrderId("THROUGHPUT_ORDER_" + i)
              .setSymbol("BTC/USD")
              .setSide(i % 2 == 0 ? Side.SIDE_BUY : Side.SIDE_SELL)
              .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
              .setQty(UDec128Util.from("1.0"))
              .setPrice(UDec128Util.from("50000.0"))
              .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
              .build();
      bh.consume(order);
    }
  }

  @Benchmark
  @BenchmarkMode(Mode.Throughput)
  @OperationsPerInvocation(100)
  public void throughputCreateOrdersOptimized(Blackhole bh) {
    for (int i = 0; i < 100; i++) {
      PsOrder order =
          ProtobufOptimizationUtil.createOptimizedOrder(
              "THROUGHPUT_ORDER_" + i,
              "BTC/USD",
              i % 2 == 0 ? Side.SIDE_BUY : Side.SIDE_SELL,
              PsOrderType.PS_ORDER_TYPE_LIMIT,
              "1.0",
              "50000.0",
              TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL);
      bh.consume(order);
    }
  }

  @Benchmark
  @BenchmarkMode(Mode.Throughput)
  @OperationsPerInvocation(100)
  public void throughputSerializeStandard(Blackhole bh) {
    for (PsOrder order : orderList) {
      byte[] result = order.toByteArray();
      bh.consume(result);
    }
  }

  @Benchmark
  @BenchmarkMode(Mode.Throughput)
  @OperationsPerInvocation(100)
  public void throughputSerializeOptimized(Blackhole bh) throws IOException {
    for (PsOrder order : orderList) {
      byte[] result = ProtobufOptimizationUtil.serializeOptimized(order);
      bh.consume(result);
    }
  }

  // ========== Memory Allocation Benchmarks ==========

  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  public void memoryAllocationStandard(Blackhole bh) {
    // Create multiple builders (high allocation)
    for (int i = 0; i < 10; i++) {
      PsOrder.Builder builder = PsOrder.newBuilder();
      ExecReport.Builder execBuilder = ExecReport.newBuilder();
      Response.Builder responseBuilder = Response.newBuilder();
      bh.consume(builder);
      bh.consume(execBuilder);
      bh.consume(responseBuilder);
    }
  }

  @Benchmark
  @BenchmarkMode(Mode.AverageTime)
  public void memoryAllocationOptimized(Blackhole bh) {
    // Reuse builders (low allocation)
    for (int i = 0; i < 10; i++) {
      PsOrder.Builder builder = ProtobufOptimizationUtil.getReusableOrderBuilder();
      ExecReport.Builder execBuilder = ProtobufOptimizationUtil.getReusableExecReportBuilder();
      Response.Builder responseBuilder = ProtobufOptimizationUtil.getReusableResponseBuilder();
      bh.consume(builder);
      bh.consume(execBuilder);
      bh.consume(responseBuilder);
    }
  }

  @TearDown
  public void tearDown() {
    // Clean up thread-local resources
    ProtobufOptimizationUtil.clearThreadLocalResources();

    // Print performance statistics
    System.out.println(
        "Optimization Performance Stats: " + ProtobufOptimizationUtil.getPerformanceStats());
  }
}
