package io.hydrax.pricestreaming.router;

import io.hydrax.pricestreaming.common.OrderType;
import io.hydrax.pricestreaming.domain.Order;
import io.hydrax.proto.metwo.match.PsOrder;
import io.hydrax.proto.metwo.match.PsOrderType;
import io.hydrax.proto.metwo.match.TimeInForce;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public interface Rule {
  // Cache for RouterHandler instances to avoid repeated lookups
  ConcurrentMap<String, RouterHandler> ROUTER_HANDLER_CACHE = new ConcurrentHashMap<>();

  // Cache for order type string conversions - pre-populated with common values
  ConcurrentMap<PsOrderType, String> ORDER_TYPE_STRING_CACHE =
      new ConcurrentHashMap<>(
          Map.of(
              PsOrderType.PS_ORDER_TYPE_MARKET, OrderType.MARKET.getName(),
              PsOrderType.PS_ORDER_TYPE_LIMIT, OrderType.LIMIT.getName(),
              PsOrderType.PS_ORDER_TYPE_STOP, OrderType.STOP.getName()));

  String getCode();

  String getType();

  List<String> getTickers();

  Set<PsOrderType> getOrdTypes();

  Set<TimeInForce> getTimeInForces(String orderType);

  List<String> getVenueMarkets();

  default RouterHandler getRouterHandler() {
    return ROUTER_HANDLER_CACHE.computeIfAbsent(
        this.getType(), RouterHandlerFactory::getRouterHandler);
  }

  default boolean match(Order order) {
    // Cache PsOrder reference to avoid repeated method calls
    PsOrder psOrder = order.getPsOrder();

    // Use cached order type conversion for better performance
    String orderType = ORDER_TYPE_STRING_CACHE.get(psOrder.getOrdType());
    if (orderType == null) {
      return false; // UNRECOGNIZED or null order type
    }

    // Perform checks in order of likelihood to fail (most selective first)
    // 1. Check ticker first (usually most selective)
    if (!this.getTickers().contains(psOrder.getSymbol())) {
      return false;
    }

    // 2. Check order type
    if (!this.getOrdTypes().contains(psOrder.getOrdType())) {
      return false;
    }

    // 3. Check time in force (least selective, check last)
    return this.getTimeInForces(orderType).contains(psOrder.getTimeInForce());
  }

  default void handle(Order order, List<String> venueCodes) {
    this.getRouterHandler().handle(this.getCode(), order, venueCodes);
  }
}
