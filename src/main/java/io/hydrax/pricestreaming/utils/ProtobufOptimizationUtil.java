package io.hydrax.pricestreaming.utils;

import com.google.protobuf.CodedInputStream;
import com.google.protobuf.CodedOutputStream;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import io.hydrax.proto.metwo.match.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for optimizing protobuf serialization and deserialization performance.
 *
 * This class provides various optimization techniques:
 * - Builder pooling and reuse
 * - Pre-allocated output streams
 * - Cached reflection methods
 * - Batch serialization operations
 * - Memory-efficient serialization patterns
 */
@Slf4j
public final class ProtobufOptimizationUtil {

  // Builder pools for reuse
  private static final ThreadLocal<PsOrder.Builder> ORDER_BUILDER_POOL =
      ThreadLocal.withInitial(PsOrder::newBuilder);

  private static final ThreadLocal<Response.Builder> RESPONSE_BUILDER_POOL =
      ThreadLocal.withInitial(Response::newBuilder);

  private static final ThreadLocal<ResponseList.Builder> RESPONSE_LIST_BUILDER_POOL =
      ThreadLocal.withInitial(ResponseList::newBuilder);

  private static final ThreadLocal<Request.Builder> REQUEST_BUILDER_POOL =
      ThreadLocal.withInitial(Request::newBuilder);

  private static final ThreadLocal<ExecReport.Builder> EXEC_REPORT_BUILDER_POOL =
      ThreadLocal.withInitial(ExecReport::newBuilder);

  private static final ThreadLocal<Trade.Builder> TRADE_BUILDER_POOL =
      ThreadLocal.withInitial(Trade::newBuilder);

  // Pre-allocated output streams for different message sizes
  private static final ThreadLocal<ByteArrayOutputStream> SMALL_STREAM_POOL =
      ThreadLocal.withInitial(() -> new ByteArrayOutputStream(512));

  private static final ThreadLocal<ByteArrayOutputStream> MEDIUM_STREAM_POOL =
      ThreadLocal.withInitial(() -> new ByteArrayOutputStream(2048));

  private static final ThreadLocal<ByteArrayOutputStream> LARGE_STREAM_POOL =
      ThreadLocal.withInitial(() -> new ByteArrayOutputStream(8192));

  // Cached reflection methods for parseFrom operations
  private static final Map<Class<?>, Method> PARSE_FROM_CACHE = new ConcurrentHashMap<>();

  // Performance statistics
  private static volatile long builderReuseCount = 0;
  private static volatile long streamReuseCount = 0;
  private static volatile long reflectionCacheHits = 0;

  private ProtobufOptimizationUtil() {
    throw new AssertionError("No instances for you!");
  }

  // ========== Builder Reuse Methods ==========

  /**
   * Get a reusable PsOrder builder from the thread-local pool.
   * The builder is automatically cleared before returning.
   */
  public static PsOrder.Builder getReusableOrderBuilder() {
    PsOrder.Builder builder = ORDER_BUILDER_POOL.get();
    builder.clear();
    builderReuseCount++;
    return builder;
  }

  /**
   * Get a reusable Response builder from the thread-local pool.
   */
  public static Response.Builder getReusableResponseBuilder() {
    Response.Builder builder = RESPONSE_BUILDER_POOL.get();
    builder.clear();
    builderReuseCount++;
    return builder;
  }

  /**
   * Get a reusable ResponseList builder from the thread-local pool.
   */
  public static ResponseList.Builder getReusableResponseListBuilder() {
    ResponseList.Builder builder = RESPONSE_LIST_BUILDER_POOL.get();
    builder.clear();
    builderReuseCount++;
    return builder;
  }

  /**
   * Get a reusable Request builder from the thread-local pool.
   */
  public static Request.Builder getReusableRequestBuilder() {
    Request.Builder builder = REQUEST_BUILDER_POOL.get();
    builder.clear();
    builderReuseCount++;
    return builder;
  }

  /**
   * Get a reusable ExecReport builder from the thread-local pool.
   */
  public static ExecReport.Builder getReusableExecReportBuilder() {
    ExecReport.Builder builder = EXEC_REPORT_BUILDER_POOL.get();
    builder.clear();
    builderReuseCount++;
    return builder;
  }

  /**
   * Get a reusable Trade builder from the thread-local pool.
   */
  public static Trade.Builder getReusableTradeBuilder() {
    Trade.Builder builder = TRADE_BUILDER_POOL.get();
    builder.clear();
    builderReuseCount++;
    return builder;
  }

  // ========== Optimized Serialization Methods ==========

  /**
   * Serialize a message using a pre-allocated output stream based on estimated size.
   */
  public static byte[] serializeOptimized(Message message) throws IOException {
    int estimatedSize = message.getSerializedSize();
    ByteArrayOutputStream stream = getOptimalStream(estimatedSize);

    try {
      stream.reset();
      CodedOutputStream codedOutput = CodedOutputStream.newInstance(stream);
      message.writeTo(codedOutput);
      codedOutput.flush();
      streamReuseCount++;
      return stream.toByteArray();
    } catch (IOException e) {
      log.warn("Failed to serialize message optimally, falling back to standard method", e);
      return message.toByteArray();
    }
  }

  /**
   * Get the optimal pre-allocated stream based on message size.
   */
  private static ByteArrayOutputStream getOptimalStream(int estimatedSize) {
    if (estimatedSize <= 512) {
      return SMALL_STREAM_POOL.get();
    } else if (estimatedSize <= 2048) {
      return MEDIUM_STREAM_POOL.get();
    } else {
      return LARGE_STREAM_POOL.get();
    }
  }

  /**
   * Batch serialize multiple messages into a single byte array.
   */
  public static byte[] serializeBatch(Iterable<? extends Message> messages) throws IOException {
    ByteArrayOutputStream batchStream = LARGE_STREAM_POOL.get();
    batchStream.reset();

    CodedOutputStream codedOutput = CodedOutputStream.newInstance(batchStream);

    for (Message message : messages) {
      // Write message size first (for later parsing)
      codedOutput.writeUInt32NoTag(message.getSerializedSize());
      // Write message
      message.writeTo(codedOutput);
    }

    codedOutput.flush();
    streamReuseCount++;
    return batchStream.toByteArray();
  }

  // ========== Optimized Deserialization Methods ==========

  /**
   * Parse a message using cached reflection for better performance.
   */
  @SuppressWarnings("unchecked")
  public static <T extends Message> T parseFromOptimized(byte[] bytes, Class<T> clazz)
      throws InvalidProtocolBufferException {
    try {
      Method parseMethod =
          PARSE_FROM_CACHE.computeIfAbsent(
              clazz,
              c -> {
                try {
                  return c.getMethod("parseFrom", byte[].class);
                } catch (NoSuchMethodException e) {
                  throw new RuntimeException(
                      "No parseFrom method found for class: " + c.getName(), e);
                }
              });

      reflectionCacheHits++;
      return (T) parseMethod.invoke(null, bytes);
    } catch (Exception e) {
      throw new InvalidProtocolBufferException("Failed to parse message: " + e.getMessage());
    }
  }

  /**
   * Parse multiple messages from a batch-serialized byte array.
   */
  public static <T extends Message> java.util.List<T> parseBatch(byte[] batchBytes, Class<T> clazz)
      throws InvalidProtocolBufferException {
    java.util.List<T> results = new java.util.ArrayList<>();
    CodedInputStream codedInput = CodedInputStream.newInstance(batchBytes);

    try {
      while (!codedInput.isAtEnd()) {
        int messageSize = codedInput.readUInt32();
        byte[] messageBytes = codedInput.readRawBytes(messageSize);
        T message = parseFromOptimized(messageBytes, clazz);
        results.add(message);
      }
    } catch (IOException e) {
      throw new InvalidProtocolBufferException("Failed to parse batch: " + e.getMessage());
    }

    return results;
  }

  // ========== High-level Optimization Methods ==========

  /**
   * Create an optimized PsOrder using reusable builder and UDec128 utility.
   */
  public static PsOrder createOptimizedOrder(
      String orderId,
      String symbol,
      Side side,
      PsOrderType orderType,
      String qty,
      String price,
      TimeInForce timeInForce) {
    return getReusableOrderBuilder()
        .setOrderId(orderId)
        .setSymbol(symbol)
        .setSide(side)
        .setOrdType(orderType)
        .setQty(UDec128Util.from(qty))
        .setPrice(UDec128Util.from(price))
        .setTimeInForce(timeInForce)
        .build();
  }

  /**
   * Create an optimized ResponseList with multiple responses.
   */
  public static ResponseList createOptimizedResponseList(
      long outSequence, java.util.List<Response> responses) {
    ResponseList.Builder builder = getReusableResponseListBuilder().setOutSequence(outSequence);

    for (Response response : responses) {
      builder.addResponses(response);
    }

    return builder.build();
  }

  /**
   * Create an optimized ExecReport using reusable builder.
   */
  public static ExecReport createOptimizedExecReport(
      String orderId, ExecType execType, OrdStatus ordStatus) {
    return getReusableExecReportBuilder()
        .setOrderId(orderId)
        .setExecType(execType)
        .setOrdStatus(ordStatus)
        .build();
  }

  // ========== Performance Monitoring ==========

  /**
   * Get performance statistics for monitoring optimization effectiveness.
   */
  public static PerformanceStats getPerformanceStats() {
    return new PerformanceStats(builderReuseCount, streamReuseCount, reflectionCacheHits);
  }

  /**
   * Reset performance statistics.
   */
  public static void resetPerformanceStats() {
    builderReuseCount = 0;
    streamReuseCount = 0;
    reflectionCacheHits = 0;
  }

  /**
   * Performance statistics holder.
   */
  public static class PerformanceStats {
    public final long builderReuseCount;
    public final long streamReuseCount;
    public final long reflectionCacheHits;

    public PerformanceStats(
        long builderReuseCount, long streamReuseCount, long reflectionCacheHits) {
      this.builderReuseCount = builderReuseCount;
      this.streamReuseCount = streamReuseCount;
      this.reflectionCacheHits = reflectionCacheHits;
    }

    @Override
    public String toString() {
      return String.format(
          "PerformanceStats{builderReuse=%d, streamReuse=%d, reflectionCacheHits=%d}",
          builderReuseCount, streamReuseCount, reflectionCacheHits);
    }
  }

  // ========== Cleanup Methods ==========

  /**
   * Clear all thread-local resources. Should be called when thread is done.
   */
  public static void clearThreadLocalResources() {
    ORDER_BUILDER_POOL.remove();
    RESPONSE_BUILDER_POOL.remove();
    RESPONSE_LIST_BUILDER_POOL.remove();
    REQUEST_BUILDER_POOL.remove();
    EXEC_REPORT_BUILDER_POOL.remove();
    TRADE_BUILDER_POOL.remove();
    SMALL_STREAM_POOL.remove();
    MEDIUM_STREAM_POOL.remove();
    LARGE_STREAM_POOL.remove();
  }
}
