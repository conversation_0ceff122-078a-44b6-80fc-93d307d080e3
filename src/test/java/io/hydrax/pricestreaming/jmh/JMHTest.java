package io.hydrax.pricestreaming.jmh;

import io.hydrax.aeron.client.ClientManager;
import io.hydrax.pricestreaming.common.Constant;
import io.hydrax.pricestreaming.domain.Order;
import io.hydrax.pricestreaming.events.OrderEvent;
import io.hydrax.pricestreaming.utils.IdUtil;
import io.hydrax.proto.metwo.match.*;
import io.vertx.core.Vertx;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.eventbus.EventBus;
import java.util.Queue;
import java.util.concurrent.atomic.AtomicInteger;
import org.agrona.concurrent.*;
import org.openjdk.jmh.annotations.*;

public class JMHTest {

  @State(Scope.Benchmark)
  public static class SharedState {
    @SuppressWarnings("unchecked")
    protected static final Queue<Integer>[] responseQueues = new OneToOneConcurrentArrayQueue[10];

    final AtomicInteger id = new AtomicInteger(0);

    final AtomicInteger threadId = new AtomicInteger(0);
    Vertx vertx;
    EventBus eventBus;

    @Setup
    public void setup() {
      for (int i = 0; i < 10; i++) {
        responseQueues[i] = new OneToOneConcurrentArrayQueue<>(128);
      }
      vertx = Vertx.vertx();
      eventBus = vertx.eventBus();
    }

    @TearDown
    public synchronized void tearDown() {
      for (int i = 0; i < 10; i++) {
        responseQueues[i].clear();
      }
    }
  }

  @State(Scope.Thread)
  public static class PerThreadState {
    AtomicInteger id;
    final ClientManager clientManager;
    final OrderEvent orderEvent;
    final AtomicBuffer buffer = new UnsafeBuffer(new byte[3000]);
    EventBus eventBus;

    public PerThreadState(ClientManager clientManager, OrderEvent orderEvent) {
      this.clientManager = clientManager;
      this.orderEvent = orderEvent;
    }

    @Setup
    public void setup(final JMHTest.SharedState sharedState) {
      id = sharedState.id;
      eventBus = sharedState.eventBus;
    }
  }

  @Benchmark
  @BenchmarkMode({Mode.All})
  @Threads(7)
  public void test(final JMHTest.PerThreadState perThreadState) {
    PsOrder order =
        PsOrder.newBuilder()
            .setOrderId(String.valueOf(perThreadState.id.getAndIncrement()))
            .setTimeInForce(TimeInForce.TIME_IN_FORCE_GOOD_TILL_CANCEL)
            .setOrdType(PsOrderType.PS_ORDER_TYPE_LIMIT)
            .setRequestType(RequestType.REQUEST_TYPE_NEW_ORDER)
            .build();
    DeliveryOptions deliveryOptions = new DeliveryOptions();
    deliveryOptions.addHeader("orderId", IdUtil.formatId(Constant.ID_SEQUENCE.getAndIncrement()));
    perThreadState
        .eventBus
        .consumer("ORDER_TEST")
        .handler(event -> perThreadState.orderEvent.onOrder((Order) event.body()));
    perThreadState.eventBus.send("ORDER_TEST", order, deliveryOptions);
  }
}
